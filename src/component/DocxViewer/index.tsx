import { useEffect, useRef } from "react";
import { renderAsync } from "docx-preview";

interface DocxPreviewProps {
  file: File | ArrayBuffer | Blob | null; // 支持 File / Blob / ArrayBuffer
  className?: string; // 自定义 className
  ignoreCss?: boolean; // 是否忽略 docx-preview 自带样式
  style?: React.CSSProperties; // 容器样式
}

const DocxPreview: React.FC<DocxPreviewProps> = ({
  file,
  className = "my-docx-preview",
  ignoreCss = false,
  style = { width: "100%", height: "400px" },
}) => {
  const previewRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!file || !previewRef.current) return;

    // 清空上一次渲染内容，避免多个文件叠加
    previewRef.current.innerHTML = "";

    renderAsync(file, previewRef.current, null, {
      className,
      ignoreCss,
    })
      .then(() => console.log("DOCX 渲染成功"))
      .catch((err) => console.error("DOCX 渲染失败:", err));
  }, [file, className, ignoreCss]);

  return <div ref={previewRef} style={style} />;
};

export default DocxPreview;
